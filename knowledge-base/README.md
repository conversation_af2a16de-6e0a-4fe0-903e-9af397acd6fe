# Cheese Knowledge Base (Docs-as-Code)

This repository folder holds a filesystem-native, Markdown-first knowledge base focused on cheese, hosting, and pairings, designed to expand to wine, meats, spices, and more.

- Start with our philosophy: see ../info-philosophy.md
- Navigation indexes live under indexes/
- Canonical entities live under entities/
- Pairings and boards live under pairings/
- Hosting guides and activities live under hosting/
- Event plans live under events/
- Recipes for sides and beverages live under recipes/
- Meta (templates, taxonomy, conventions) live under _meta/

Use links and tags rather than deep folders. See _meta/conventions.md for naming, IDs, and tagging.

Quickstart: use _meta/checklists/add-knowledge-item.md and include an Identifiers block (Color, Rind, Paste/Texture, Shape/Format) plus 1–3 sources per entity.
Maintainers: for MoC hygiene, see _meta/playbooks/update-indexes.md and _meta/checklists/update-indexes.md.


