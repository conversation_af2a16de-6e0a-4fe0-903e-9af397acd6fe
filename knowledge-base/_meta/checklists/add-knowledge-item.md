# Checklist: add-knowledge-item

Use this when adding any entity (cheese, wine, meat, accompaniment), pairing, or guide.

## Retrieval-first discovery
- [ ] Use the retrieval tool (codebase-retrieval) for all repo searches (avoid manual browsing)
- [ ] Search repo for existing item/aliases to avoid duplicates
- [ ] Search for related items (pairings, events) to add backlinks
- [ ] Scan for placeholders (verify/tbd) that this addition can resolve

## Create from template
- [ ] Copy appropriate template from _meta/templates/
- [ ] Choose a stable ASCII id/filename; keep diacritics in title/aliases
- [ ] Fill essential frontmatter: id, title, tags, created, updated

## Enhanced content (Comprehensive tier)
- [ ] Profile: 2-4 sentences with origin story, production method, distinctive characteristics
- [ ] Identifiers block with detailed descriptions (Color, Rind, Paste/Texture, Shape/Format)
- [ ] Serving: temperature, handling, storage, ripeness indicators
- [ ] Pairings: specific wine categories, accompaniments with rationale
- [ ] Producers & Affinage: key producers, methods, quality standards
- [ ] Fun Facts & Stories: historical anecdotes, production quirks, cultural significance
- [ ] Quick Explore: links to related cheeses, pairings, index sections
- [ ] Tags: include flavor/* if helpful (≤3), following taxonomy

## Index & links
- [ ] Add to relevant index (e.g., indexes/cheeses.md)
- [ ] Link from related entities or pairings when practical

## Verification
- [ ] Mark uncertainties explicitly (verify/tbd) and add a TODO bullet
- [ ] Cite 3–5 canonical sources with access dates (see Conventions → Sources & Citations)
- [ ] Verify Quick Explore links resolve and create stubs if needed

## Commit
- [ ] Descriptive commit message referencing affected IDs
- [ ] Optional: add to current event doc if relevant



## Fast path (Baseline in ≤90s)
- [ ] Retrieval: confirm not already present (search name + alias)
- [ ] Paste skeleton, fill: id/title/tags + 1–2 line Profile + one Serving bullet
- [ ] Add to at least one index (MoC)
- [ ] One related link (pairing/region/producer)
- [ ] Commit with: feat(kb): add <type> <name> (ids: <id>)

## Optional speed boosters
- [ ] Batch similar items (copy shared tags/frontmatter)
- [ ] Create pairing stub if none exists and link from both sides
- [ ] Add region entry in By Region section if missing

## Post-commit sweep
- [ ] Retrieval: search id appears in indexes/ and at least one other file
- [ ] Fix any verify/tbd with a follow-up task
