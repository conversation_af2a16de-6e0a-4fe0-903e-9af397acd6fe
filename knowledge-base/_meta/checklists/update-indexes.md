# Checklist: index-upkeep

Use this after adding/editing entities or on a weekly sweep.

## Discovery
- [ ] List new/changed entities/pairings since last sweep
- [ ] Search for orphans (entities not in indexes/)
- [ ] Search for alias gaps (common variants missing from `aliases:`)

## MoC updates
- [ ] Add each new entity to By Style and By Milk; Region when notable
- [ ] Ensure pairings are linked from both entity sides
- [ ] Keep bullets alphabetized within each section

## Taxonomy
- [ ] If new regions/styles introduced, update _meta/taxonomy.md
- [ ] Normalize tag spelling (lowercase, hyphenated)

## Validation
- [ ] Click links in indexes to ensure they resolve
- [ ] Confirm each new id appears in ≥1 index and ≥1 other file

## Commit
- [ ] Commit with: chore(kb): index upkeep — <summary> (ids: <id1>, <id2>)

