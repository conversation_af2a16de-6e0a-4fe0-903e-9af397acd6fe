# Playbook: Index Upkeep (Maps of Content)

Purpose: keep navigation accurate, discoverable, and current as the corpus grows.

## When to run this
- After adding/editing any entity or pairing
- Weekly quick sweep; monthly deeper audit

## Inputs
- Recent commits list (what changed)
- Retrieval queries (see below)
- Taxonomy file: ../taxonomy.md

## Steps

### 1) Retrieval-first discovery
- Use the retrieval tool (codebase-retrieval); avoid manual browsing for discovery
- Search for new/modified IDs since last sweep; list files in entities/, pairings/, events/
- Find orphans: search for `id:` that do not appear in indexes/
- Find alias gaps: search for names that appear in text but not as `aliases:`

### 2) Update MoCs (indexes/*)
- Add each new entity to at least one section by style and milk; add region if notable
- For Wines index: ensure every wine referenced in pairings/ appears under both By Region and By Style
- Ensure pairings are linked from both sides’ entity pages when appropriate
- Keep sections alphabetical within each bullet for scanability

### 3) Taxonomy alignment
- If new regions/styles/tags were introduced, update _meta/taxonomy.md examples and families
- Normalize tag spelling (lowercase, hyphenated); avoid one-offs

### 4) Validation
- Click through relative links from the index to ensure they resolve
- Spot-check frontmatter of 2–3 recent entities (id/title/tags present)
- Run retrieval to confirm each new id appears in ≥1 index and ≥1 other file

### 5) Commit
- Group all index/taxonomy touch-ups in a single commit when possible
- Message template: `chore(kb): index upkeep — add <items>, align taxonomy (ids: <id1>, <id2>)`

## Retrieval query patterns
- New since date: search commit messages for `feat(kb): add` and `ids:`
- Orphans: search for `id: cheese:` files not referenced in indexes/
- Alias candidates: search the repo for common diacritic/brand variants and compare to `aliases:` lists
- Unlinked pairings: search for `pairings/cheese-wine/*__*.md` and ensure both entity files reference the pairing

## Quality bar
- Every entity appears in at least one index section (style or milk); region when meaningful
- Alphabetical ordering within bullets; consistent separators
- No broken relative links in indexes/

## Notes
- Prefer minimal, frequent edits over large sporadic ones
- If a section becomes long, split logically (e.g., Style → Washed‑rind vs Tomme)
- Keep examples in taxonomy small but representative; avoid exhaustive lists there

