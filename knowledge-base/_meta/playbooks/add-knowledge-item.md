# Playbook: Adding a knowledge item

Purpose: a repeatable sub-routine for adding high-quality items while keeping the corpus coherent.

## 1) Discover (retrieval-first)
- Use the retrieval tool (codebase-retrieval) for all repo searches before any web lookups
- Search by canonical name and common aliases/diacritics
- Search for near-duplicates (similar slugs) and placeholders (verify/tbd)
- Identify where this item should appear in MoCs and who it should link to

## 2) Define
- Decide stable id (ASCII) and filename slug; keep title with diacritics
- Pick tag families (entity/*, milk/*, style/*, region/*, etc.) from taxonomy
- Determine completeness target (Stub, Baseline, Enriched, Verified)

## 3) Develop
- Create from template; fill essential frontmatter including producer, certification, detailed aging
- Write a rich Profile (2-4 sentences) with origin story, production method, and distinctive characteristics
- Add comprehensive Identifiers block (Color, Rind, Paste/Texture, Shape/Format) with aging details
- Add detailed Serving guidance (temperature, handling, storage, ripeness indicators)
- Include specific Pairings with rationale (wine categories, accompaniments, seasonal notes)
- Add Producers & Affinage section with key producers, methods, and quality standards
- Include Fun Facts & Stories with historical anecdotes, production quirks, cultural significance
- Add Quick Explore links to related cheeses, pairings, and index sections
- Tags: include flavor/* where it aids retrieval/pairing; keep within taxonomy


## 4) Deliver
- Add to indexes (MoCs); check relative links and backlinks
- Cite 3-5 canonical sources with access dates (see Conventions — Sources & Citations)
- Ensure Quick Explore links connect to existing entities and create stubs if needed
- Run a quick retrieval sweep (codebase-retrieval) to catch orphans and alias gaps
- Commit with descriptive message: "feat(kb): add <type> <name> (ids: <id>)"
- If you added/renamed regions/styles, update _meta/taxonomy.md accordingly (and run the Index Upkeep playbook)

## Notes & patterns
- Diacritics policy: ASCII IDs/filenames; preserve diacritics in titles/aliases
- Goat lactic family (Loire): common items include Bûcheron, Sainte-Maure de Touraine, Chabichou du Poitou
- Pair Loire goat cheeses with Loire whites (Touraine/Sancerre) as a baseline

## Future automation
- Link checker, frontmatter linter, MoC coverage report, TODO/VERIFY sweep



## Speed-focused plays

### The 90‑second add (Baseline tier)
1) Retrieval: search name + common alias; ensure no duplicate file exists
2) Copy cheese/wine template; paste skeleton below
3) Fill title, id, tags, and a 1–2 sentence Profile; add one Serving bullet
4) Drop into indexes (at least one MoC); save
5) Commit with the template message

Skeleton to paste:
```
---
id: <type>:<slug>
title: <Title with diacritics>
aliases: []
tags: [entity/<type>, <key tags>]
created: <YYYY-MM-DD>
updated: <YYYY-MM-DD>
---

## Profile
<one–three sentences>

## Identifiers
- Color:
- Rind:
- Paste/Texture:
- Shape/Format:

## Serving
- <one bullet>

## Sources
- Wikipedia — <page>: <url>
```

### Quick retrieval query patterns
- Name variants: "<name>" OR "<alias>" OR producer name
- Find orphans: search for the filename slug without matches in indexes/
- Find placeholders to resolve: search terms verify, tbd, TODO
- Backlinks: search for the id across pairings/, events/, hosting/

### Slug and alias rules (fast)
- IDs/filenames ASCII; titles keep diacritics
- Add at least one alias when a diacritic/brand variant exists
- Prefer kebab‑case: sainte-maure-de-touraine, chabichou-du-poitou

### Tagging cheat sheet
- Cheese: milk/*, texture/*, style/*, region/<cc>/<subregion>
- Loire goat lactic: milk/goat, texture/soft, style/fresh, rind/ash or natural, region/fr/loire or specific subregion
- Alpine: style/alpine; cooked‑pressed usually texture/semi‑hard→hard
- Triple‑cream: style/triple‑cream; texture/soft; rind/bloomy
- Flavor: add ≤2 flavor/* tags max where they aid retrieval/pairing (e.g., flavor/lactic, flavor/nutty)

### Batch‑add pattern (3–10 similar items)
- Decide shared tags and copy skeletons in one go
- Fill only the differing fields (title, id, region)
- Update MoC in a single edit block; then commit once

### Commit message template
- feat(kb): add <type> <name> — baseline profile, index links (ids: <id>)

### Timeboxed quality gates
- Baseline ≤ 90s; Enriched pass later if needed
- Always: frontmatter complete, in at least one index, one related link
