# Conventions

## Naming & IDs
- Filenames: kebab-case.md (e.g., comte-young.md)
- IDs: stable, human-readable (e.g., cheese:comte:young)
- Aliases: list common variants/transliterations within frontmatter; include diacritic variants and common brand spellings (IDs and filenames remain ASCII)

## Frontmatter (required fields)
- id, title, tags, created, updated
- Entities: add domain fields (e.g., milk, style, texture, region)
- Pairings: left_entity, right_entity, pairing_type, rationale

## Linking
- Use relative links between files
- Provide backlinks section when useful
- MoCs act as curated entry points; update them when adding content

## Tags
- Use families from _meta/taxonomy.md
- Keep concise and meaningful; avoid near-duplicates


## Sources & Citations
- Use simple inline citations: Label — Title: URL (no footnote system)
- Source hierarchy (prefer earlier): official/regulatory > producer/consortium > Wikipedia > Cheese.com > reputable retailer/affineur
- Keep URLs stable (entity pages over query URLs); avoid tracking parameters
- Minimum for entities: 1–3 sources; for pairings: include at least one rationale source if not purely experiential

## Contribution
- Start with a stub, then iterate with tasting notes and sources
- Mark uncertain facts with TODO/verify
- After events, add post-mortems and learnings

