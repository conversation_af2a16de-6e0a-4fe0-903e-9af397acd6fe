# Information Philosophy

## Core Values

### 1. **Filesystem-Native, Future-Proof**
- **Markdown-first**: All content in plain text, human-readable, version-controllable
- **Tool-agnostic**: Works with any editor, publishable via static site generators
- **No vendor lock-in**: Can migrate between Obsidian, Notion, MkDocs, Docusaurus without data loss
- **Git-friendly**: Meaningful diffs, collaborative editing, branching for experiments

### 2. **Structured Flexibility Over Rigid Hierarchy**
- **Shallow folders + rich metadata**: Avoid deep trees that become brittle
- **Tags and frontmatter**: Enable multiple organizational schemes simultaneously
- **Links over location**: Relationships matter more than folder placement
- **Maps of Content (MoCs)**: Curated navigation paths, not just folder browsing

### 3. **Explicit Relationships**
- **Many-to-many as first-class docs**: Pairings deserve their own files with rationale
- **Bidirectional linking**: Every relationship should be discoverable from both sides
- **Context over categorization**: Why things go together matters more than where they're filed
- **Stable IDs**: Entities have persistent identifiers for reliable cross-references

### 4. **Progressive Disclosure**
- **Start simple, grow complex**: Basic stubs → detailed profiles → rich interconnections
- **Layered information**: Quick reference → detailed notes → research citations
- **Multiple entry points**: Indexes, tags, search, and browsing all valid navigation
- **Graceful degradation**: Core info accessible even without advanced tooling

### 5. **Practical Utility Over Academic Completeness**
- **Action-oriented**: Every piece of info should support decision-making or learning
- **Event-driven**: Knowledge serves real gatherings, tastings, and experiences
- **Reusable patterns**: Templates and checklists that scale across events
- **Learning from experience**: Post-event notes feed back into planning knowledge

## Documentation Principles

### Content Structure
- **Frontmatter consistency**: Every file has metadata for parsing and filtering
- **Semantic naming**: Filenames reflect content, not arbitrary numbering
- **Consistent templates**: Similar entities follow similar structures
- **Version awareness**: Track creation/update dates, source attribution

### Writing Style
- **Concise but complete**: Essential info upfront, details available on demand
- **Descriptive over prescriptive**: Capture characteristics, let users decide
- **Sensory language**: Taste, texture, aroma descriptions that aid recognition
- **Practical context**: Serving suggestions, portion sizes, timing considerations


### Identification-first blocks
- Every entity includes an "Identifiers" section to speed recognition: Color, Rind, Paste/Texture, Shape/Format.
- Keep it a scannable list (3–6 bullets). Optimizes recognition at tastings and improves retrieval quality.

### Relationship Documentation
- **Explicit rationale**: Why do these things pair well? What's the theory?
- **Constraint awareness**: What doesn't work? When to avoid combinations?
- **Occasion sensitivity**: Context matters—casual vs. formal, season, audience
- **Intensity matching**: Balance flavors, textures, and alcohol levels thoughtfully

### Maintenance Philosophy
- **Living documents**: Regular updates based on new experiences
- **Source attribution**: Track where information comes from
- **Collaborative friendly**: Clear conventions for multiple contributors
- **Quality over quantity**: Better to have fewer, well-documented items than many shallow ones

## Information Architecture Principles

### Entity Design
- **Single source of truth**: One canonical file per cheese/wine/producer
- **Rich metadata**: Tags, regions, styles, flavor profiles in structured format
- **Stable identifiers**: IDs that don't break when titles change
- **Alias support**: Multiple names for the same entity (regional variations, etc.)

### Taxonomy Strategy
- **Faceted classification**: Multiple independent dimensions (milk, texture, region, style)
- **Controlled vocabularies**: Consistent tag families prevent proliferation
- **Hierarchical when useful**: Region/country, style/substyle where natural
- **Extensible design**: Easy to add new facets without restructuring

### Navigation Design
- **Multiple pathways**: Browsing, searching, following links all supported
- **Contextual discovery**: Related items surfaced based on current content
- **Progressive filtering**: Start broad, narrow down by facets
- **Serendipitous exploration**: Encourage discovery of unexpected connections

### Scalability Considerations
- **Domain expansion**: Wine, meats, spices fit the same patterns
- **Event scaling**: From intimate tastings to large gatherings
- **Knowledge depth**: From beginner-friendly to expert-level detail
- **Geographic expansion**: Regional specialties and local producers

## Quality Standards

### Accuracy
- **Source verification**: Prefer primary sources (producers, official regions)
- **Experience validation**: Personal tasting notes complement research
- **Uncertainty acknowledgment**: Mark provisional or unverified information
- **Regular review**: Revisit and update based on new information


### Source hierarchy and citation style
- Prefer sources in this order: official/regulatory (AOP/PDO/INAO, consortium) > producer/consortium sites > Wikipedia > Cheese.com > reputable retailers/affineurs.
- Use simple inline citations: Label — Title: URL (accessed YYYY-MM-DD). Avoid footnote systems; keep URLs stable (no tracking params).
- Minimum per entity: 3–5 sources for comprehensive entries; pairings should cite at least one rationale source when not purely experiential.
- Include access dates for all web sources to track information currency and enable future verification.

### Completeness
- **Essential fields**: Core metadata always present
- **Progressive enhancement**: Start with basics, add detail over time
- **Cross-references**: Link to related entities and pairings
- **Context provision**: Enough background for informed decisions

### Consistency
- **Template adherence**: Similar entities follow similar structures
- **Naming conventions**: Predictable patterns for files and identifiers
- **Tagging discipline**: Use established vocabularies, create new tags thoughtfully
- **Style guide**: Consistent voice, formatting, and organization


### Tagging for retrieval
- Use flavor/* tags sparingly (≤3) to aid search and pairing suggestions; adhere to _meta/taxonomy.md.

### Usability
- **Scannable format**: Headers, lists, and formatting aid quick reading
- **Action orientation**: Clear next steps for planning and preparation
- **Error tolerance**: Graceful handling of missing or incomplete information
- **Tool compatibility**: Works well across different viewing and editing environments

## Evolution and Maintenance

### Content Lifecycle
1. **Stub creation**: Basic entity with minimal required fields
2. **Experience integration**: Add tasting notes, pairing discoveries
3. **Research enhancement**: Incorporate authoritative sources and context
4. **Community validation**: Review and refinement through use
5. **Archival consideration**: Mark outdated or superseded information

### Contribution Guidelines
- **Start with stubs**: Better to capture something than nothing
- **Cite sources**: Track where information originates
- **Personal vs. general**: Distinguish subjective experience from objective facts
- **Collaborative editing**: Clear conventions for multiple contributors
- **Quality gates**: Review process for significant additions or changes

### Technology Evolution
- **Format stability**: Markdown and YAML frontmatter as long-term formats
- **Tool flexibility**: Avoid features that lock us into specific applications
- **Export capability**: Always maintain ability to migrate to new platforms
- **Backup strategy**: Version control plus periodic exports to other formats

## Enforcement and Quality Assurance

### Required Checks for Every New/Edited File
- Frontmatter includes: id, title, tags, created, updated
- Tags use approved families from _meta/taxonomy.md
- ID is stable, unique, and matches filename slug
- Relative links resolve (no broken links)
- If an entity: added to the appropriate Index (MoC)
- If a pairing: includes a clear rationale and constraints
- TODO markers are specific and tracked to follow-up tasks

### How We Enforce This (lightweight for now)
- Pre-commit checklist (manual): run through the bullets above
- Add to Indexes: update indexes/ files when new entities/pairings are created
- Periodic audit (monthly): scan for missing frontmatter, stray tags, and unlinked files
- Review significant additions (new sections, taxonomies) with a short PR description linking to this philosophy

### Edit Workflow (golden path)
1) Create stub from template in _meta/templates/
2) Fill essential fields and minimal Profile
3) Link to at least one related item (pairing, region, producer)
4) Add to relevant index
5) Commit with a descriptive message referencing affected IDs

### Governance
- Update this document when patterns or standards change
- Prefer incremental improvements; avoid disruptive restructures
- Document exceptions and their rationale

## Continuous Improvement (What we've learned and will enforce)

### Learnings so far
- Alias normalization matters (diacritics, brand/spelling variants) → treat aliases as first-class; keep IDs stable and ASCII-safe.
- Index hygiene drives discovery → every entity must appear in at least one MoC; pairings should be linked from both sides.
- Small, factual enrichments (AOP, style cues, aging) dramatically increase usefulness without heavy effort.
- Event docs are effective forcing functions to add practical guidance (serving temps, portions, timelines).

### Planned standards (next)
- Completeness tiers for entities (see below) to make “done enough for now” explicit.
- Pairing documents include rationale, constraints, and intensity mapping as standard sections.
- Diacritics policy: filenames and IDs may be ASCII; titles/aliases preserve diacritics. Always include an alias for the alternate spelling.
- Index coverage check: new entities must be added to at least one index before merge.

### Retrieval-first operations (as corpus grows)
- Always search before browsing: use retrieval to locate entities, related items, and prior art.
- Use retrieval to:
  - Find orphans (entities not referenced by any index/pairing/event)
  - Surface near-duplicate names and alias gaps
  - Discover missing backlinks (e.g., cheese mentions in events without links)
  - Collect candidates for normalization (regions, styles, tags)
- Periodically run targeted searches (e.g., “verify”, “<tbd>”, “TODO”) and convert placeholders to specific follow-ups.

### Completeness tiers (entity maturity levels)
- Stub: id, title, tags, created/updated + 1–2 sentence profile.
- Baseline: adds region, milk, style, rind, aging; at least 1 related link; appears in an index.
- Enriched: serving/portion guidance, sourcing tips, 2–3 pairings (with constraints), basic sources, producers section.
- Comprehensive: adds Fun Facts & Stories, Producers & Affinage, Quick Explore links, 3-5 authoritative sources with access dates.
- Verified: authoritative sources cited, ambiguities resolved, tasting notes and date, comprehensive cross-linking.

### Pairing documentation standard
- Must include: rationale, constraints, intensity match (align/contrast), occasion/seasonality.
- Prefer explicit levers: acidity, tannin, sweetness, salt, fat, texture.
- Link to all referenced entities; add cross-links from entity pages when practical.

### Index hygiene and orphans
- Every entity appears in ≥1 index; retrieval used monthly to list non-indexed entities.
- Create backlog items for orphans; either index them or intentionally mark as experimental.

### Tag governance
- Only add new tag families via a short proposal in _meta/taxonomy.md with examples.
- Avoid one-off tags; prefer existing families (milk/*, texture/*, style/*, region/*, entity/*).

### Source and verification policy
- Prefer primary/authoritative sources; record them in Sources.
- Mark uncertainty explicitly (verify/tbd), then schedule a retrieval sweep to resolve.

### Automation backlog (lightweight)
- Link checker (relative links resolve).
- Frontmatter linter (required keys, tag families).
- MoC coverage report (entities not referenced by indexes).
- TODO/VERIFY report (surface placeholders into a mini task list).

---

*This philosophy guides all decisions about structure, content, and maintenance of our knowledge base. When in doubt, refer back to these principles.*
